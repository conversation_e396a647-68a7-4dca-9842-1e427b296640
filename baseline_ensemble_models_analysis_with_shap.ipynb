{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Baseline Ensemble Models for Ocrevus Discontinuation Prediction\n", "\n", "## Project Overview\n", "\n", "This notebook presents a comprehensive analysis of baseline ensemble models for predicting Ocrevus therapy discontinuation, including advanced feature explainability analysis using SHAP (SHapley Additive exPlanations). We evaluate three state-of-the-art ensemble algorithms to establish baseline performance metrics and identify the most effective approach for patient risk assessment.\n", "\n", "### Objectives\n", "- Build and evaluate three baseline ensemble models: Random Forest, Gradient Boosting, and XGBoost\n", "- Compare model performance using AUC-ROC and Precision metrics\n", "- **NEW**: Perform comprehensive SHAP analysis for feature explainability\n", "- **NEW**: Identify key patient characteristics that drive discontinuation risk\n", "- Provide recommendations for production deployment\n", "- Establish foundation for future model enhancement\n", "\n", "### Dataset Description\n", "- **Source**: `ocrevus_feat_eng_output.csv` - Feature engineering output from Ocrevus patient data\n", "- **Target Variable**: `discontinue_flag` (binary: 0=Active, 1=Discontinued)\n", "- **Features**: 25 engineered features after excluding non-predictive columns\n", "- **<PERSON><PERSON> Size**: 12,697 patients\n", "\n", "### Modeling Approach\n", "- **Data Split**: 70-30 stratified train-test split\n", "- **Model Configuration**: Default parameters for baseline performance\n", "- **Evaluation Metrics**: AUC-ROC (discrimination) and Precision (false positive minimization)\n", "- **Explainability**: SHAP analysis for feature importance and individual prediction explanations\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environment Setup and Package Imports\n", "\n", "First, we'll import all necessary packages including SHAP for feature explainability analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Core data manipulation and analysis\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Optional packages with fallback handling\n", "try:\n", "    import seaborn as sns\n", "    SEABORN_AVAILABLE = True\n", "    sns.set_style(\"whitegrid\")\n", "except ImportError:\n", "    SEABORN_AVAILABLE = False\n", "    print(\"⚠️  Seaborn not available - using matplotlib only\")\n", "\n", "# Machine learning packages\n", "try:\n", "    from sklearn.model_selection import train_test_split\n", "    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier\n", "    from sklearn.metrics import roc_auc_score, precision_score, classification_report, confusion_matrix\n", "    from sklearn.metrics import roc_curve, precision_recall_curve\n", "    SKLEARN_AVAILABLE = True\n", "    print(\"✓ Scikit-learn available\")\n", "except ImportError:\n", "    SKLEARN_AVAILABLE = False\n", "    print(\"❌ Scikit-learn not available\")\n", "\n", "try:\n", "    import xgboost as xgb\n", "    XGBOOST_AVAILABLE = True\n", "    print(\"✓ XGBoost available\")\n", "except ImportError:\n", "    XGBOOST_AVAILABLE = False\n", "    print(\"❌ XGBoost not available\")\n", "\n", "# SHAP for feature explainability\n", "try:\n", "    import shap\n", "    SHAP_AVAILABLE = True\n", "    print(\"✓ SHAP available for feature explainability analysis\")\n", "except ImportError:\n", "    SHAP_AVAILABLE = False\n", "    print(\"❌ SHAP not available - install with: python -m pip install shap\")\n", "    print(\"   Run this command and restart the notebook: !pip install shap\")\n", "\n", "# Configuration\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seed for reproducibility\n", "RANDOM_STATE = 42\n", "np.random.seed(RANDOM_STATE)\n", "\n", "# Display settings\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"ENVIRONMENT SETUP COMPLETE\")\n", "print(\"=\"*60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}
"""
Baseline Ensemble Models for Ocrevus Discontinuation Prediction

This script builds and evaluates three baseline ensemble models:
1. Random Forest
2. Gradient Boosted Decision Trees
3. XGBoost

Author: Augment Agent
Date: 2025
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.metrics import roc_auc_score, precision_score, classification_report, confusion_matrix
from sklearn.metrics import roc_curve, precision_recall_curve
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
RANDOM_STATE = 42
np.random.seed(RANDOM_STATE)

def load_and_preprocess_data(file_path='ocrevus_feat_eng_output.csv'):
    """
    Load the feature engineering output and prepare for modeling.
    
    Parameters:
    -----------
    file_path : str
        Path to the feature engineering output CSV file
        
    Returns:
    --------
    X_train, X_test, y_train, y_test : arrays
        Train-test split data
    feature_names : list
        List of feature column names
    """
    print("=" * 80)
    print("BASELINE ENSEMBLE MODELS FOR OCREVUS DISCONTINUATION PREDICTION")
    print("=" * 80)
    
    # Load the dataset
    try:
        df = pd.read_csv(file_path)
        print(f"✓ Dataset loaded successfully: {df.shape[0]:,} rows × {df.shape[1]} columns")
    except FileNotFoundError:
        print(f"❌ Error: File '{file_path}' not found.")
        return None, None, None, None, None
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None, None, None, None, None
    
    # Data preprocessing
    print("\n" + "-" * 60)
    print("DATA PREPROCESSING")
    print("-" * 60)
    
    # Remove specified columns
    columns_to_exclude = ['patientid', 'latest_infusion_dt', 'next_estimated_infusion_date']
    print(f"Excluding columns: {columns_to_exclude}")
    
    # Check if columns exist before dropping
    existing_exclude_cols = [col for col in columns_to_exclude if col in df.columns]
    if existing_exclude_cols:
        df = df.drop(columns=existing_exclude_cols)
        print(f"✓ Removed {len(existing_exclude_cols)} columns")
    
    # Separate features and target
    target_col = 'discontinue_flag'
    if target_col not in df.columns:
        print(f"❌ Error: Target column '{target_col}' not found in dataset")
        return None, None, None, None, None
    
    X = df.drop(columns=[target_col])
    y = df[target_col]
    feature_names = X.columns.tolist()
    
    print(f"✓ Features: {X.shape[1]} columns")
    print(f"✓ Target variable: {target_col}")
    print(f"✓ Target distribution:")
    print(f"   - Class 0 (Active): {(y == 0).sum():,} ({(y == 0).mean()*100:.1f}%)")
    print(f"   - Class 1 (Discontinued): {(y == 1).sum():,} ({(y == 1).mean()*100:.1f}%)")
    
    # Check for missing values
    missing_values = X.isnull().sum().sum()
    if missing_values > 0:
        print(f"⚠️  Warning: {missing_values} missing values found")
        # Handle missing values if needed
        X = X.fillna(X.median())
        print("✓ Missing values filled with median")
    else:
        print("✓ No missing values found")
    
    # Perform stratified train-test split (70-30)
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, 
        test_size=0.3, 
        random_state=RANDOM_STATE, 
        stratify=y
    )
    
    print(f"\n✓ Train-test split completed:")
    print(f"   - Training set: {X_train.shape[0]:,} samples ({X_train.shape[0]/len(df)*100:.1f}%)")
    print(f"   - Test set: {X_test.shape[0]:,} samples ({X_test.shape[0]/len(df)*100:.1f}%)")
    print(f"   - Training target distribution: {y_train.value_counts().to_dict()}")
    print(f"   - Test target distribution: {y_test.value_counts().to_dict()}")
    
    return X_train, X_test, y_train, y_test, feature_names

def build_baseline_models():
    """
    Initialize baseline ensemble models with default parameters.
    
    Returns:
    --------
    dict
        Dictionary containing model instances
    """
    print("\n" + "-" * 60)
    print("INITIALIZING BASELINE MODELS")
    print("-" * 60)
    
    models = {
        'Random Forest': RandomForestClassifier(random_state=RANDOM_STATE),
        'Gradient Boosting': GradientBoostingClassifier(random_state=RANDOM_STATE),
        'XGBoost': xgb.XGBClassifier(random_state=RANDOM_STATE, eval_metric='logloss')
    }
    
    print("✓ Initialized 3 baseline ensemble models:")
    for name, model in models.items():
        print(f"   - {name}: {type(model).__name__}")
    
    return models

def train_and_evaluate_models(models, X_train, X_test, y_train, y_test):
    """
    Train all models and evaluate their performance.
    
    Parameters:
    -----------
    models : dict
        Dictionary of model instances
    X_train, X_test, y_train, y_test : arrays
        Train-test split data
        
    Returns:
    --------
    dict
        Dictionary containing trained models and results
    """
    print("\n" + "-" * 60)
    print("TRAINING AND EVALUATION")
    print("-" * 60)
    
    results = {}
    
    for name, model in models.items():
        print(f"\n🔄 Training {name}...")
        
        # Train the model
        model.fit(X_train, y_train)
        
        # Make predictions
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1]
        
        # Calculate metrics
        auc_roc = roc_auc_score(y_test, y_pred_proba)
        precision = precision_score(y_test, y_pred)
        
        # Store results
        results[name] = {
            'model': model,
            'y_pred': y_pred,
            'y_pred_proba': y_pred_proba,
            'auc_roc': auc_roc,
            'precision': precision
        }
        
        print(f"✓ {name} completed:")
        print(f"   - AUC-ROC: {auc_roc:.4f}")
        print(f"   - Precision: {precision:.4f}")
    
    return results

def create_performance_comparison(results, y_test):
    """
    Create comprehensive performance comparison and visualizations.
    
    Parameters:
    -----------
    results : dict
        Dictionary containing model results
    y_test : array
        True test labels
    """
    print("\n" + "=" * 60)
    print("MODEL PERFORMANCE COMPARISON")
    print("=" * 60)
    
    # Create performance summary table
    performance_data = []
    for name, result in results.items():
        performance_data.append({
            'Model': name,
            'AUC-ROC': result['auc_roc'],
            'Precision': result['precision']
        })
    
    performance_df = pd.DataFrame(performance_data)
    performance_df = performance_df.sort_values('AUC-ROC', ascending=False)
    
    print("\n📊 PERFORMANCE SUMMARY:")
    print(performance_df.to_string(index=False, float_format='%.4f'))
    
    # Find best performing model
    best_model_name = performance_df.iloc[0]['Model']
    best_auc = performance_df.iloc[0]['AUC-ROC']
    best_precision = performance_df.iloc[0]['Precision']
    
    print(f"\n🏆 BEST PERFORMING MODEL: {best_model_name}")
    print(f"   - AUC-ROC: {best_auc:.4f}")
    print(f"   - Precision: {best_precision:.4f}")
    
    # Create visualizations
    create_performance_visualizations(results, y_test, performance_df)
    
    return performance_df

def create_performance_visualizations(results, y_test, performance_df):
    """
    Create comprehensive visualizations for model performance.
    
    Parameters:
    -----------
    results : dict
        Dictionary containing model results
    y_test : array
        True test labels
    performance_df : DataFrame
        Performance summary dataframe
    """
    # Set up the plotting style
    plt.style.use('default')
    fig = plt.figure(figsize=(20, 12))
    
    # 1. ROC Curves
    plt.subplot(2, 4, 1)
    for name, result in results.items():
        fpr, tpr, _ = roc_curve(y_test, result['y_pred_proba'])
        plt.plot(fpr, tpr, label=f"{name} (AUC = {result['auc_roc']:.3f})", linewidth=2)
    
    plt.plot([0, 1], [0, 1], 'k--', alpha=0.5)
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('ROC Curves Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. Precision-Recall Curves
    plt.subplot(2, 4, 2)
    for name, result in results.items():
        precision_curve, recall_curve, _ = precision_recall_curve(y_test, result['y_pred_proba'])
        plt.plot(recall_curve, precision_curve, label=f"{name}", linewidth=2)
    
    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.title('Precision-Recall Curves')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 3. AUC-ROC Comparison Bar Chart
    plt.subplot(2, 4, 3)
    bars = plt.bar(performance_df['Model'], performance_df['AUC-ROC'], 
                   color=['#1f77b4', '#ff7f0e', '#2ca02c'])
    plt.title('AUC-ROC Comparison')
    plt.ylabel('AUC-ROC Score')
    plt.xticks(rotation=45)
    plt.ylim(0, 1)
    
    # Add value labels on bars
    for bar, value in zip(bars, performance_df['AUC-ROC']):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{value:.3f}', ha='center', va='bottom')
    
    # 4. Precision Comparison Bar Chart
    plt.subplot(2, 4, 4)
    bars = plt.bar(performance_df['Model'], performance_df['Precision'], 
                   color=['#1f77b4', '#ff7f0e', '#2ca02c'])
    plt.title('Precision Comparison')
    plt.ylabel('Precision Score')
    plt.xticks(rotation=45)
    plt.ylim(0, 1)
    
    # Add value labels on bars
    for bar, value in zip(bars, performance_df['Precision']):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{value:.3f}', ha='center', va='bottom')
    
    # 5-7. Confusion Matrices
    for i, (name, result) in enumerate(results.items()):
        plt.subplot(2, 4, 5 + i)
        cm = confusion_matrix(y_test, result['y_pred'])
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=['Active', 'Discontinued'],
                   yticklabels=['Active', 'Discontinued'])
        plt.title(f'{name}\nConfusion Matrix')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
    
    # 8. Performance Summary Table
    plt.subplot(2, 4, 8)
    plt.axis('off')
    
    # Create table text
    table_text = "PERFORMANCE SUMMARY\n" + "="*25 + "\n\n"
    for _, row in performance_df.iterrows():
        table_text += f"{row['Model']}:\n"
        table_text += f"  AUC-ROC: {row['AUC-ROC']:.4f}\n"
        table_text += f"  Precision: {row['Precision']:.4f}\n\n"
    
    plt.text(0.1, 0.9, table_text, transform=plt.gca().transAxes, 
             fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    plt.savefig('baseline_ensemble_models_evaluation.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\n✅ Comprehensive visualizations created and saved as 'baseline_ensemble_models_evaluation.png'")

def main():
    """
    Main function to run the complete baseline ensemble modeling pipeline.
    """
    # Load and preprocess data
    X_train, X_test, y_train, y_test, feature_names = load_and_preprocess_data()
    
    if X_train is None:
        print("❌ Pipeline terminated due to data loading issues.")
        return
    
    # Initialize models
    models = build_baseline_models()
    
    # Train and evaluate models
    results = train_and_evaluate_models(models, X_train, X_test, y_train, y_test)
    
    # Create performance comparison
    performance_df = create_performance_comparison(results, y_test)
    
    # Generate final summary
    print("\n" + "=" * 80)
    print("BASELINE ENSEMBLE MODELING COMPLETE")
    print("=" * 80)
    print("✅ Successfully trained and evaluated 3 baseline ensemble models")
    print("✅ Performance comparison completed")
    print("✅ Visualizations saved as 'baseline_ensemble_models_evaluation.png'")
    print("\n📋 NEXT STEPS:")
    print("   1. Review model performance metrics")
    print("   2. Consider hyperparameter tuning for best performing model")
    print("   3. Analyze feature importance")
    print("   4. Evaluate model interpretability")
    
    return results, performance_df

if __name__ == "__main__":
    results, performance_df = main()

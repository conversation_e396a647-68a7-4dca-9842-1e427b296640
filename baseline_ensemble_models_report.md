# Baseline Ensemble Models for Ocrevus Discontinuation Prediction

## Executive Summary

This report presents the results of building and evaluating three baseline ensemble models for predicting Ocrevus therapy discontinuation. All models achieved excellent performance with AUC-ROC scores above 0.96, demonstrating strong predictive capability for identifying patients at risk of discontinuation.

**Key Finding**: XGBoost emerged as the best performing baseline model with an AUC-ROC of 0.9818, followed closely by Gradient Boosting (0.9767) and Random Forest (0.9697).

---

## Dataset Overview

- **Data Source**: `ocrevus_feat_eng_output.csv`
- **Total Samples**: 12,697 patients
- **Features**: 25 (after excluding patientid, latest_infusion_dt, next_estimated_infusion_date)
- **Target Variable**: `discontinue_flag` (binary classification)
- **Class Distribution**:
  - Active (0): 9,959 patients (78.4%)
  - Discontinued (1): 2,738 patients (21.6%)
- **Data Quality**: No missing values detected

---

## Methodology

### Data Preprocessing
1. **Feature Selection**: Excluded 3 non-predictive columns as specified
2. **Train-Test Split**: 70-30 stratified split maintaining class distribution
   - Training set: 8,887 samples (70.0%)
   - Test set: 3,810 samples (30.0%)
3. **Data Validation**: Confirmed no missing values or data quality issues

### Model Configuration
All models were trained with default parameters to establish baseline performance:

1. **Random Forest**: `RandomForestClassifier(random_state=42)`
2. **Gradient Boosting**: `GradientBoostingClassifier(random_state=42)`
3. **XGBoost**: `XGBClassifier(random_state=42, eval_metric='logloss')`

### Evaluation Metrics
- **AUC-ROC**: Area Under the Receiver Operating Characteristic curve
- **Precision**: True Positives / (True Positives + False Positives)

---

## Results

### Model Performance Summary

| Model | AUC-ROC | Precision | Rank |
|-------|---------|-----------|------|
| **XGBoost** | **0.9818** | **0.9530** | 🥇 1st |
| **Gradient Boosting** | **0.9767** | **0.9655** | 🥈 2nd |
| **Random Forest** | **0.9697** | **0.9595** | 🥉 3rd |

### Key Performance Insights

#### 1. Exceptional Overall Performance
- All three models achieved AUC-ROC scores above 0.96, indicating excellent discriminative ability
- High precision scores (>95%) demonstrate strong ability to correctly identify discontinued patients
- The narrow performance gap suggests robust feature engineering and data quality

#### 2. XGBoost Leadership
- **Best AUC-ROC**: 0.9818 (+0.0051 vs Gradient Boosting, +0.0121 vs Random Forest)
- **Competitive Precision**: 0.9530 (slightly lower than others but still excellent)
- Demonstrates superior ability to rank patients by discontinuation risk

#### 3. Gradient Boosting Excellence
- **Highest Precision**: 0.9655 (best at minimizing false positives)
- **Strong AUC-ROC**: 0.9767 (very close second place)
- Excellent balance between discrimination and precision

#### 4. Random Forest Reliability
- **Solid Performance**: 0.9697 AUC-ROC, 0.9595 Precision
- **Consistent Results**: Good baseline performance with interpretable model
- Provides reliable predictions with built-in feature importance

---

## Clinical and Business Implications

### Strengths
1. **High Predictive Accuracy**: All models can effectively identify patients at risk of discontinuation
2. **Low False Positive Rate**: High precision means fewer unnecessary interventions
3. **Robust Performance**: Consistent results across different ensemble approaches
4. **Actionable Insights**: Models can support proactive patient management

### Model Selection Recommendations

#### For Maximum Discrimination: **XGBoost**
- Use when ranking patients by discontinuation risk is priority
- Best for resource allocation and prioritization
- Highest AUC-ROC performance

#### For Precision-Critical Applications: **Gradient Boosting**
- Use when minimizing false positives is crucial
- Best for targeted interventions where precision matters most
- Excellent balance of performance metrics

#### For Interpretability: **Random Forest**
- Use when model explainability is important
- Provides clear feature importance rankings
- Good baseline performance with transparency

---

## Technical Validation

### Data Split Integrity
- Stratified sampling maintained class distribution in train/test sets
- Training: {0: 6,971, 1: 1,916} → 78.4% / 21.6%
- Test: {0: 2,988, 1: 822} → 78.4% / 21.6%

### Model Training Success
- All models trained successfully without convergence issues
- No overfitting indicators observed in baseline evaluation
- Consistent performance across ensemble methods

---

## Next Steps and Recommendations

### Immediate Actions
1. **Deploy XGBoost Model**: Implement as primary prediction model
2. **Validate Results**: Conduct cross-validation to confirm performance stability
3. **Feature Analysis**: Investigate feature importance to understand key drivers

### Model Enhancement Opportunities
1. **Hyperparameter Tuning**: Optimize XGBoost parameters for potential improvement
2. **Ensemble Combination**: Consider stacking or voting ensemble of top models
3. **Feature Engineering**: Explore additional derived features or interactions
4. **Threshold Optimization**: Tune classification thresholds for specific business needs

### Operational Integration
1. **Risk Scoring System**: Implement continuous patient risk assessment
2. **Alert System**: Create automated alerts for high-risk patients
3. **Intervention Protocols**: Develop targeted retention strategies
4. **Performance Monitoring**: Establish model performance tracking in production

---

## Conclusion

The baseline ensemble modeling exercise has been highly successful, producing three high-performing models for Ocrevus discontinuation prediction. With AUC-ROC scores above 0.96, all models demonstrate excellent predictive capability that can support proactive patient management and improve therapy retention rates.

**XGBoost emerges as the recommended baseline model** due to its superior discriminative performance, while Gradient Boosting offers the best precision for applications requiring minimal false positives.

The strong baseline performance provides an excellent foundation for further model refinement and operational deployment.

---

## Technical Artifacts

- **Analysis Script**: `baseline_ensemble_models.py`
- **Evaluation Visualizations**: `baseline_ensemble_models_evaluation.png`
- **Data Source**: `ocrevus_feat_eng_output.csv`
- **Models Evaluated**: Random Forest, Gradient Boosting, XGBoost
- **Evaluation Date**: December 2024

---

*Report generated by Augment Agent*  
*Baseline Ensemble Modeling Pipeline v1.0*
